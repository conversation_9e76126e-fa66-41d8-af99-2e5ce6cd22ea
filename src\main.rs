slint::include_modules!();

use std::fs;
use std::path::{Path, PathBuf};
use slint::{SharedString, VecModel, ModelRc};
use std::sync::{Arc, Mutex};

fn read_directory_files(dir_path: &Path) -> Result<(Vec<String>, Vec<String>), std::io::Error> {
    let mut display_files = Vec::new();
    let mut actual_files = Vec::new();

    // Read directory entries
    let entries = fs::read_dir(dir_path)?;

    for entry in entries {
        let entry = entry?;
        let file_name = entry.file_name();
        let metadata = entry.metadata()?;

        // Convert OsString to String, skip if conversion fails
        if let Some(name) = file_name.to_str() {
            // Add a prefix to distinguish files from directories
            if metadata.is_dir() {
                display_files.push(format!("📁 {}", name));
                actual_files.push(format!("📁 {}", name));
            } else {
                display_files.push(format!("📄 {}", name));
                actual_files.push(format!("📄 {}", name));
            }
        }
    }

    // Sort both vectors in the same way (directories first, then files)
    let mut combined: Vec<(String, String)> = display_files.into_iter().zip(actual_files.into_iter()).collect();
    combined.sort_by(|a, b| {
        let a_is_dir = a.0.starts_with("📁");
        let b_is_dir = b.0.starts_with("📁");

        match (a_is_dir, b_is_dir) {
            (true, false) => std::cmp::Ordering::Less,  // Directories first
            (false, true) => std::cmp::Ordering::Greater, // Files after directories
            _ => a.0.cmp(&b.0), // Same type, sort alphabetically
        }
    });

    let (display_files, actual_files): (Vec<String>, Vec<String>) = combined.into_iter().unzip();
    Ok((display_files, actual_files))
}

fn read_file_as_hex(file_path: &Path) -> Result<String, std::io::Error> {
    let bytes = fs::read(file_path)?;

    let mut hex_string = String::new();
    let mut line_bytes = Vec::new();

    for (i, byte) in bytes.iter().enumerate() {
        if i % 16 == 0 && i > 0 {
            // Add ASCII representation
            hex_string.push_str("  |");
            for &b in &line_bytes {
                if b >= 32 && b <= 126 {
                    hex_string.push(b as char);
                } else {
                    hex_string.push('.');
                }
            }
            hex_string.push_str("|\n");
            line_bytes.clear();
        }

        if i % 16 == 0 {
            hex_string.push_str(&format!("{:08X}: ", i));
        }

        hex_string.push_str(&format!("{:02X} ", byte));
        line_bytes.push(*byte);

        if i % 8 == 7 && i % 16 != 15 {
            hex_string.push(' '); // Extra space every 8 bytes
        }
    }

    // Handle the last line if it's not complete
    if !line_bytes.is_empty() {
        let remaining = 16 - line_bytes.len();
        for _ in 0..remaining {
            hex_string.push_str("   ");
            if line_bytes.len() + (16 - remaining) == 8 {
                hex_string.push(' ');
            }
        }
        hex_string.push_str("  |");
        for &b in &line_bytes {
            if b >= 32 && b <= 126 {
                hex_string.push(b as char);
            } else {
                hex_string.push('.');
            }
        }
        hex_string.push_str("|\n");
    }

    Ok(hex_string)
}

fn main() -> Result<(), slint::PlatformError> {
    let ui = AppWindow::new()?;

    // Shared state for tracking current folder and files
    let current_folder = Arc::new(Mutex::new(Option::<PathBuf>::None));
    let file_names = Arc::new(Mutex::new(Vec::<String>::new()));

    // Set up the pick_folder callback
    let ui_weak = ui.as_weak();
    let current_folder_clone = current_folder.clone();
    let file_names_clone = file_names.clone();
    ui.on_pick_folder(move || {
        let ui = ui_weak.unwrap();

        // Show folder picker dialog
        if let Some(folder_path) = rfd::FileDialog::new().pick_folder() {
            println!("Selected folder: {}", folder_path.display());

            // Read directory contents
            match read_directory_files(&folder_path) {
                Ok((display_files, actual_files)) => {
                    println!("Found {} items in directory", display_files.len());

                    // Store the current folder and file names
                    *current_folder_clone.lock().unwrap() = Some(folder_path);
                    *file_names_clone.lock().unwrap() = actual_files;

                    // Convert Vec<String> to VecModel<SharedString> for Slint
                    let file_list: Vec<SharedString> = display_files.into_iter()
                        .map(|f| SharedString::from(f))
                        .collect();

                    // Update the file_list property in the UI
                    let model = VecModel::from(file_list);
                    ui.set_file_list(ModelRc::new(model));

                    // Reset selection
                    ui.set_selected_file_index(-1);

                    println!("File list updated successfully");
                }
                Err(e) => {
                    eprintln!("Error reading directory: {}", e);
                }
            }
        } else {
            println!("No folder selected");
        }
    });

    // Set up the select_file callback
    let ui_weak = ui.as_weak();
    ui.on_select_file(move |index| {
        let ui = ui_weak.unwrap();
        ui.set_selected_file_index(index);
        println!("Selected file index: {}", index);
    });

    // Set up the open_hero_file callback
    let ui_weak = ui.as_weak();
    let current_folder_clone = current_folder.clone();
    let file_names_clone = file_names.clone();
    ui.on_open_hero_file(move || {
        let ui = ui_weak.unwrap();
        let selected_index = ui.get_selected_file_index();

        if selected_index < 0 {
            println!("No file selected");
            return;
        }

        let current_folder_guard = current_folder_clone.lock().unwrap();
        let file_names_guard = file_names_clone.lock().unwrap();

        if let Some(ref folder_path) = *current_folder_guard {
            if let Some(file_name) = file_names_guard.get(selected_index as usize) {
                // Skip directories (they start with 📁)
                if file_name.starts_with("📁") {
                    println!("Cannot open directory as hero file");
                    return;
                }

                // Remove the 📄 prefix to get actual filename
                let actual_filename = file_name.strip_prefix("📄 ").unwrap_or(file_name);
                let file_path = folder_path.join(actual_filename);

                println!("Opening hero file: {}", file_path.display());

                match read_file_as_hex(&file_path) {
                    Ok(hex_content) => {
                        ui.set_hero_text(SharedString::from(hex_content));
                        println!("Hero file loaded successfully");
                    }
                    Err(e) => {
                        eprintln!("Error reading hero file: {}", e);
                        ui.set_hero_text(SharedString::from(format!("Error reading file: {}", e)));
                    }
                }
            }
        }
    });

    ui.run()
}