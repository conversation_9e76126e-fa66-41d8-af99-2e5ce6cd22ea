slint::include_modules!();

use std::fs;
use std::path::Path;
use slint::{SharedString, VecModel, ModelRc};

fn main() -> Result<(), slint::PlatformError> {
    let ui = AppWindow::new()?;

    // Set up the pick_folder callback
    let ui_weak = ui.as_weak();
    ui.on_pick_folder(move || {
        let ui = ui_weak.unwrap();

        // Show folder picker dialog
        if let Some(folder_path) = rfd::FileDialog::new().pick_folder() {
            println!("Selected folder: {}", folder_path.display());

            // Read directory contents
            match read_directory_files(&folder_path) {
                Ok(files) => {
                    println!("Found {} items in directory", files.len());

                    // Convert Vec<String> to VecModel<SharedString> for Slint
                    let file_list: Vec<SharedString> = files.into_iter()
                        .map(|f| SharedString::from(f))
                        .collect();

                    // Update the file_list property in the UI
                    let model = VecModel::from(file_list);
                    ui.set_file_list(ModelRc::new(model));

                    println!("File list updated successfully");
                }
                Err(e) => {
                    eprintln!("Error reading directory: {}", e);
                }
            }
        } else {
            println!("No folder selected");
        }
    });

    ui.run()
}

fn read_directory_files(dir_path: &Path) -> Result<Vec<String>, std::io::Error> {
    let mut files = Vec::new();

    // Read directory entries
    let entries = fs::read_dir(dir_path)?;

    for entry in entries {
        let entry = entry?;
        let file_name = entry.file_name();
        let metadata = entry.metadata()?;

        // Convert OsString to String, skip if conversion fails
        if let Some(name) = file_name.to_str() {
            // Add a prefix to distinguish files from directories
            if metadata.is_dir() {
                files.push(format!("📁 {}", name));
            } else {
                files.push(format!("📄 {}", name));
            }
        }
    }

    // Sort files alphabetically (directories first, then files)
    files.sort_by(|a, b| {
        let a_is_dir = a.starts_with("📁");
        let b_is_dir = b.starts_with("📁");

        match (a_is_dir, b_is_dir) {
            (true, false) => std::cmp::Ordering::Less,  // Directories first
            (false, true) => std::cmp::Ordering::Greater, // Files after directories
            _ => a.cmp(b), // Same type, sort alphabetically
        }
    });

    Ok(files)
}