import { Button, VerticalBox, HorizontalBox, ListView, TextEdit, LineEdit } from "std-widgets.slint";

export component AppWindow inherits Window {
    title: "MMerge Hack Tool";

    // Make window resizable with default size
    preferred-width: 1200px;
    preferred-height: 800px;
    min-width: 800px;
    min-height: 600px;

    // Properties for data binding
    in-out property <[string]> file_list: [];
    in-out property <string> hero_text: "";
    in-out property <string> search_text: "";
    in-out property <string> file_name: "No file selected";
    in-out property <string> file_content_hex: "";
    in-out property <int> selected_file_index: -1;

    // Callbacks
    callback pick_folder();
    callback select_file(int);
    callback open_hero_file();
    callback erase_events();
    callback save_hero();
    callback find_hero();
    callback extract_selection_as_hero();
    callback place_hero_at_cursor();
    callback open_file();
    callback save_file();
    callback save_file_as();

    HorizontalBox {
        spacing: 10px;
        padding: 10px;

        // Left Column
        VerticalBox {
            spacing: 10px;
            horizontal-stretch: 3;

            Button {
                text: "Pick Folder";
                clicked => { root.pick_folder(); }
            }

            // File list - flexible height, scales with window
            Rectangle {
                border-width: 1px;
                border-color: #ccc;
                vertical-stretch: 2;

                ListView {
                    for file[index] in root.file_list: Rectangle {
                        background: index == root.selected_file_index ? #e0e0ff : transparent;
                        border-width: index == root.selected_file_index ? 1px : 0px;
                        border-color: #4080ff;

                        TouchArea {
                            clicked => {
                                root.select_file(index);
                            }
                        }

                        Text {
                            text: file;
                            font-size: 12px;
                            color: index == root.selected_file_index ? #000080 : black;
                        }
                    }
                }
            }

            Button {
                text: "Open Hero File";
                clicked => { root.open_hero_file(); }
            }

            // Text window - flexible height, scales with window
            Rectangle {
                border-width: 1px;
                border-color: #ccc;
                vertical-stretch: 3;

                TextEdit {
                    text <=> root.hero_text;
                    font-size: 11px;
                    wrap: no-wrap;
                    read-only: true;
                }
            }

            Button {
                text: "Erase Events";
                clicked => { root.erase_events(); }
            }

            Button {
                text: "Save Hero";
                clicked => { root.save_hero(); }
            }
        }

        // Center Column
        VerticalBox {
            spacing: 20px;
            alignment: center;
            horizontal-stretch: 1;

            Button {
                text: "Find Hero";
                clicked => { root.find_hero(); }
            }

            LineEdit {
                text <=> root.search_text;
                placeholder-text: "Enter search text...";
            }

            Button {
                text: "Extract Selection as Hero";
                clicked => { root.extract_selection_as_hero(); }
            }

            Button {
                text: "Place Hero at Cursor";
                clicked => { root.place_hero_at_cursor(); }
            }
        }

        // Right Column
        VerticalBox {
            spacing: 10px;
            horizontal-stretch: 3;

            Button {
                text: "Open File";
                clicked => { root.open_file(); }
            }

            Text {
                text: root.file_name;
                font-size: 12px;
                horizontal-alignment: center;
            }

            // File content in hex format - flexible height, scales with window
            Rectangle {
                border-width: 1px;
                border-color: #ccc;
                vertical-stretch: 1;

                TextEdit {
                    text <=> root.file_content_hex;
                    font-size: 10px;
                    read-only: true;
                    wrap: word-wrap;
                }
            }

            Button {
                text: "Save File";
                clicked => { root.save_file(); }
            }

            Button {
                text: "Save File As";
                clicked => { root.save_file_as(); }
            }
        }
    }
}
